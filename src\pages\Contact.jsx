import { Phone, Mail, MapPin, Clock } from 'lucide-react'

const Contact = () => {

  return (
    <div className="contact">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <div className="page-hero-content">
            <h1>Contact Us</h1>
            <p>
              Monday - Friday: 9:00 AM - 5:00 PM<br />
              Saturday: 10:00 AM - 3:00 PM<br />
              Sunday: By Appointment
            </p>
          </div>
        </div>
      </section>



      {/* Map Section */}
      <section className="map-section">
        <div className="container">
          <h2>Dallas Texas Location</h2>
          <div className="map-container">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d10209.066929413777!2d-96.70313932315138!3d32.95611127481004!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x864c1ee4b1df8357%3A0xe8f6835084eaed0f!2s601%20N%20Plano%20Rd%2C%20Richardson%2C%20TX%2075081!5e0!3m2!1sen!2sus!4v1752412788315!5m2!1sen!2sus"
              width="100%"
              height="450"
              style={{ border: 0, borderRadius: 'var(--radius-lg)' }}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="TonnuGroup Office Location"
            ></iframe>
          </div>
        </div>
      </section>

      {/* Tampa Location Section */}
      <section className="map-section">
        <div className="container">
          <h2>Tampa, Florida Location</h2>
          <div className="map-container">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3522.2!2d-82.4572!3d27.9506!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x88c2c0d5e8e8e8e8%3A0x8e8e8e8e8e8e8e8e!2sTampa%2C%20FL%2C%20USA!5e0!3m2!1sen!2sus!4v1000000000000!5m2!1sen!2sus"
              width="100%"
              height="450"
              style={{ border: 0, borderRadius: 'var(--radius-lg)' }}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="TonnuGroup Tampa Office Location"
            ></iframe>
          </div>
        </div>
      </section>



      {/* Emergency Contact */}
      <section className="emergency-contact">
        <div className="container">
          <div className="emergency-content">
            <h2>Need Immediate Assistance?</h2>
            <p>
              For urgent tax matters or time-sensitive real estate transactions, 
              don't hesitate to call us directly. We're here to help when you need us most.
            </p>
            <div className="emergency-info">
              <div className="emergency-item">
                <h3>Tax Emergencies</h3>
                <p>IRS notices, audit support, deadline issues</p>
                <a href="tel:9727439331" className="emergency-phone">(*************</a>
              </div>
              <div className="emergency-item">
                <h3>Real Estate Urgent</h3>
                <p>Contract issues, closing problems, time-sensitive matters</p>
                <a href="tel:9727439331" className="emergency-phone">(*************</a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Contact
