@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Remove default button styles */
button {
  font-family: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  text-decoration: none;
  color: inherit;
}

/* Remove default list styles */
ul, ol {
  list-style: none;
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #d4af37;
  outline-offset: 2px;
}
