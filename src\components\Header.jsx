import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Menu, X, Phone, Mail } from 'lucide-react'
import logoImage from '../assets/images/TNGheader1.jpg'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="header">
      <div className="header-top">
        <div className="container">
          <div className="contact-info">
            <div className="contact-item">
              <Phone size={16} />
              <span>(*************</span>
            </div>
            <div className="contact-item">
              <Mail size={16} />
              <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
      
      <nav className="navbar">
        <div className="container">
          <div className="nav-content">
            <Link to="/" className="logo">
              <img src={logoImage} alt="TonnuGroup" className="logo-image" />
            </Link>
            
            <div className={`nav-menu ${isMenuOpen ? 'active' : ''}`}>
              <Link to="/" className="nav-link" onClick={() => setIsMenuOpen(false)}>
                Home
              </Link>
              <Link to="/about" className="nav-link" onClick={() => setIsMenuOpen(false)}>
                About Us
              </Link>
              <Link to="/services" className="nav-link" onClick={() => setIsMenuOpen(false)}>
                Services
              </Link>
              <Link to="/gallery" className="nav-link" onClick={() => setIsMenuOpen(false)}>
                Gallery
              </Link>
              <Link to="/faq" className="nav-link" onClick={() => setIsMenuOpen(false)}>
                FAQ
              </Link>
              <Link to="/contact" className="nav-link" onClick={() => setIsMenuOpen(false)}>
                Find Us
              </Link>
              <Link to="/contact" className="cta-button" onClick={() => setIsMenuOpen(false)}>
                Contact
              </Link>
            </div>
            
            <button className="menu-toggle" onClick={toggleMenu}>
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </nav>
    </header>
  )
}

export default Header
