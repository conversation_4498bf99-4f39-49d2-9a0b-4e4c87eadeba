import { useState } from 'react'
import { Building, Users, Award, FileText } from 'lucide-react'

const Gallery = () => {
  const [activeCategory, setActiveCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'All' },
    { id: 'office', name: 'Our Office' },
    { id: 'team', name: 'Team Events' },
    { id: 'awards', name: 'Awards & Recognition' },
    { id: 'community', name: 'Community Involvement' }
  ]

  const galleryItems = [
    {
      id: 1,
      category: 'office',
      title: 'Modern Office Space',
      description: 'Our welcoming reception area where clients feel at home',
      icon: Building
    },
    {
      id: 2,
      category: 'office',
      title: 'Conference Room',
      description: 'Private meeting spaces for confidential consultations',
      icon: Building
    },
    {
      id: 3,
      category: 'team',
      title: 'Team Meeting',
      description: 'Our weekly team meetings ensure we stay aligned on client needs',
      icon: Users
    },
    {
      id: 4,
      category: 'team',
      title: 'Training Session',
      description: 'Continuous education keeps our team at the forefront of industry changes',
      icon: Users
    },
    {
      id: 5,
      category: 'awards',
      title: 'Excellence Award 2023',
      description: 'Recognized for outstanding client service in tax preparation',
      icon: Award
    },
    {
      id: 6,
      category: 'awards',
      title: 'Top Real Estate Broker',
      description: 'Awarded top broker status for three consecutive years',
      icon: Award
    },
    {
      id: 7,
      category: 'community',
      title: 'Tax Preparation Workshop',
      description: 'Free community workshop helping families understand tax basics',
      icon: FileText
    },
    {
      id: 8,
      category: 'community',
      title: 'First-Time Homebuyer Seminar',
      description: 'Educational seminar for first-time homebuyers in our community',
      icon: FileText
    }
  ]

  const filteredItems = activeCategory === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === activeCategory)

  return (
    <div className="gallery">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <div className="page-hero-content">
            <h1>Gallery</h1>
            <p>Take a look at our office, team, and community involvement</p>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="gallery-section">
        <div className="container">
          {/* Category Filter */}
          <div className="gallery-filters">
            {categories.map(category => (
              <button
                key={category.id}
                className={`filter-btn ${activeCategory === category.id ? 'active' : ''}`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Gallery Grid */}
          <div className="gallery-grid">
            {filteredItems.map(item => (
              <div key={item.id} className="gallery-item">
                <div className="gallery-image">
                  <div className="image-placeholder">
                    <item.icon size={60} />
                  </div>
                  <div className="gallery-overlay">
                    <h3>{item.title}</h3>
                    <p>{item.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <div className="no-results">
              <p>No items found in this category.</p>
            </div>
          )}
        </div>
      </section>

      {/* Office Tour Section */}
      <section className="office-tour">
        <div className="container">
          <div className="section-header">
            <h2>Visit Our Office</h2>
            <p>We'd love to meet you in person</p>
          </div>
          <div className="content-split">
            <div className="content-left">
              <h3>Professional Environment</h3>
              <p>
                Our modern office space is designed with our clients in mind. From the moment 
                you walk through our doors, you'll experience a professional yet welcoming 
                atmosphere where you can feel comfortable discussing your financial needs.
              </p>
              <div className="office-features">
                <div className="feature-item">
                  <Building size={20} />
                  <span>Private consultation rooms</span>
                </div>
                <div className="feature-item">
                  <Building size={20} />
                  <span>Secure document handling</span>
                </div>
                <div className="feature-item">
                  <Building size={20} />
                  <span>Comfortable waiting area</span>
                </div>
                <div className="feature-item">
                  <Building size={20} />
                  <span>Accessible location with parking</span>
                </div>
              </div>
            </div>
            <div className="content-right">
              <div className="office-info">
                <h4>Office Hours</h4>
                <div className="hours-list">
                  <div className="hours-item">
                    <span>Monday - Friday</span>
                    <span>9:00 AM - 6:00 PM</span>
                  </div>
                  <div className="hours-item">
                    <span>Saturday</span>
                    <span>10:00 AM - 4:00 PM</span>
                  </div>
                  <div className="hours-item">
                    <span>Sunday</span>
                    <span>By Appointment</span>
                  </div>
                </div>
                <div className="office-address">
                  <h4>Location</h4>
                  <p>
                    601 N Plano Rd<br />
                    Richardson, TX 75081
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Highlights */}
      <section className="team-highlights">
        <div className="container">
          <div className="section-header">
            <h2>Our Team in Action</h2>
            <p>Dedicated professionals working for your success</p>
          </div>
          <div className="highlights-grid">
            <div className="highlight-item">
              <div className="highlight-image">
                <Users size={40} />
              </div>
              <h3>Collaborative Approach</h3>
              <p>Our team works together to ensure you receive comprehensive service across all our specialties.</p>
            </div>
            <div className="highlight-item">
              <div className="highlight-image">
                <Award size={40} />
              </div>
              <h3>Continuous Learning</h3>
              <p>We stay current with industry changes through regular training and professional development.</p>
            </div>
            <div className="highlight-item">
              <div className="highlight-image">
                <FileText size={40} />
              </div>
              <h3>Community Focus</h3>
              <p>We believe in giving back through educational workshops and community involvement.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Gallery
