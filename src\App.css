/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Palette */
  --primary-black: #1a1a1a;
  --secondary-black: #2d2d2d;
  --primary-gold: #d4af37;
  --secondary-gold: #f4e4a6;
  --accent-gold: #b8941f;
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #6c757d;
  --dark-gray: #343a40;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-secondary: 'Playfair Display', Georgia, serif;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-xxl: 4rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
  --shadow-gold: 0 4px 15px rgba(212, 175, 55, 0.3);

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--primary-black);
  background-color: var(--white);
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

h1 {
  font-size: 2.5rem;
  color: var(--primary-black);
}

h2 {
  font-size: 2rem;
  color: var(--primary-black);
}

h3 {
  font-size: 1.5rem;
  color: var(--primary-black);
}

h4 {
  font-size: 1.25rem;
  color: var(--primary-black);
}

p {
  margin-bottom: var(--spacing-sm);
  color: var(--dark-gray);
}

.highlight {
  color: var(--primary-gold);
  position: relative;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  color: var(--white);
  box-shadow: var(--shadow-gold);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-black);
  border: 2px solid var(--primary-black);
}

.btn-secondary:hover {
  background: var(--primary-black);
  color: var(--white);
  transform: translateY(-2px);
}

.btn-white-outline {
  background: var(--white);
  color: var(--primary-black);
  border: 2px solid var(--primary-black);
}

.btn-white-outline:hover {
  background: var(--primary-black);
  color: var(--white);
  transform: translateY(-2px);
}

/* Section Spacing */
section {
  padding: var(--spacing-xxl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-header h2 {
  margin-bottom: var(--spacing-sm);
}

.section-header p {
  font-size: 1.1rem;
  color: var(--medium-gray);
  max-width: 600px;
  margin: 0 auto;
}

/* Content Layouts */
.content-split {
  display: grid;
  gap: var(--spacing-xl);
  align-items: center;
}

@media (min-width: 768px) {
  .content-split {
    grid-template-columns: 1fr 1fr;
  }
}

/* Image Placeholders */
.image-placeholder, .placeholder-content {
  background: linear-gradient(135deg, var(--light-gray), #e9ecef);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--medium-gray);
  border: 2px dashed var(--medium-gray);
}

.placeholder-content svg {
  margin-bottom: var(--spacing-sm);
  opacity: 0.6;
}

/* Team Photo Container */
.team-photo-container {
  border-radius: var(--radius-lg);
  overflow: hidden;
  min-height: 300px;
  box-shadow: var(--shadow-md);
}

.team-photo {
  width: 100%;
  height: 100%;
  min-height: 300px;
  object-fit: cover;
  object-position: center;
  display: block;
}

/* Header Styles */
.header {
  background: var(--white);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-top {
  background: var(--primary-black);
  color: var(--white);
  padding: 4px 0;
}

.contact-info {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: flex-end;
  align-items: center;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-xs);
}

.contact-item a {
  color: inherit;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer .contact-item a {
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.footer .contact-item a:hover {
  color: var(--primary-gold);
}

.footer .contact-item {
  flex-wrap: nowrap;
  align-items: center;
}

/* Center the Contact Info section */
.footer-section:last-child {
  text-align: center;
}

.footer-section:last-child .contact-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.footer-section:last-child .contact-item {
  justify-content: center;
}

.navbar {
  padding: var(--spacing-xs) 0;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-text {
  font-family: var(--font-secondary);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-black);
  text-decoration: none;
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-image {
  height: 45px;
  max-width: 200px;
  width: auto;
  object-fit: contain;
  transition: transform var(--transition-fast);
}

.logo-image:hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  color: var(--primary-black);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-gold);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gold);
  transition: width var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

.cta-button {
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-gold);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--primary-black);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .contact-info {
    justify-content: center;
    gap: var(--spacing-md);
  }

  .contact-item {
    font-size: 0.8rem;
  }

  .menu-toggle {
    display: block;
  }

  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--white);
    flex-direction: column;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-link {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--light-gray);
    width: 100%;
    text-align: center;
  }

  .cta-button {
    margin-top: var(--spacing-sm);
    text-align: center;
  }

  .logo-image {
    height: 35px;
    max-width: 150px;
  }

  .navbar {
    padding: 4px 0;
  }
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
  color: var(--white);
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(26, 26, 26, 0.85), rgba(45, 45, 45, 0.7));
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 3;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: var(--spacing-md);
  color: var(--white);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xl);
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* Page Hero */
.page-hero {
  background: linear-gradient(135deg, var(--primary-black), var(--secondary-black));
  color: var(--white);
  padding: var(--spacing-xxl) 0;
  text-align: center;
}

.page-hero-content h1 {
  color: var(--white);
  margin-bottom: var(--spacing-sm);
}

.page-hero-content p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
}

/* Services Preview */
.services-preview {
  background: var(--light-gray);
}

.services-grid {
  display: grid;
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.service-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border-top: 4px solid var(--primary-gold);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  color: var(--white);
}

.service-card h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--primary-black);
}

.service-card p {
  margin-bottom: var(--spacing-md);
  color: var(--medium-gray);
}

.service-link {
  color: var(--primary-gold);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-fast);
}

.service-link:hover {
  color: var(--accent-gold);
}

/* Why Choose Us */
.why-choose-us {
  background: var(--white);
}

.features-list {
  margin: var(--spacing-lg) 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--dark-gray);
}

.feature-item svg {
  color: var(--primary-gold);
  flex-shrink: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: var(--radius-lg);
  color: var(--white);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  font-family: var(--font-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 767px) {
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
  }

  section {
    padding: var(--spacing-lg) 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .hero {
    min-height: 60vh;
  }

  .hero-image {
    object-position: center center;
  }
}

/* Testimonials */
.testimonials {
  background: var(--light-gray);
}

.testimonials-grid {
  display: grid;
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.testimonial-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.testimonial-card:hover {
  transform: translateY(-4px);
}

.stars {
  display: flex;
  gap: 2px;
  margin-bottom: var(--spacing-md);
  color: var(--primary-gold);
}

.testimonial-card p {
  font-style: italic;
  margin-bottom: var(--spacing-md);
  color: var(--dark-gray);
}

.testimonial-author strong {
  color: var(--primary-black);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.testimonial-author span {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--primary-black), var(--secondary-black));
  color: var(--white);
  text-align: center;
}

.cta-content h2 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
}

.cta-content p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* Footer */
.footer {
  background: var(--primary-black);
  color: var(--white);
  padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
  color: var(--primary-gold);
  margin-bottom: var(--spacing-md);
}

.footer-title {
  font-size: 1.5rem;
  font-family: var(--font-secondary);
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-xs);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-gold);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--white);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: var(--primary-gold);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-md);
}

.footer-bottom-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color var(--transition-fast);
}

.footer-bottom-links a:hover {
  color: var(--primary-gold);
}

/* About Page */
.our-story {
  background: var(--white);
}

.mission-values {
  background: var(--light-gray);
}

.values-grid {
  display: grid;
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.value-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.value-card:hover {
  transform: translateY(-4px);
}

.value-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  color: var(--white);
}

.team-section {
  background: var(--white);
}

.team-grid {
  display: grid;
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.team-member {
  text-align: center;
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.team-member:hover {
  transform: translateY(-4px);
}

.member-photo {
  width: 150px;
  height: 150px;
  margin: 0 auto var(--spacing-md);
  border-radius: 50%;
  overflow: hidden;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.member-title {
  color: var(--primary-gold);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.member-bio {
  color: var(--medium-gray);
  font-size: 0.9rem;
  line-height: 1.5;
}

.why-choose {
  background: var(--light-gray);
}

.features-grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.features-grid .feature-item {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.features-grid .feature-item h4 {
  color: var(--primary-black);
  margin-bottom: var(--spacing-sm);
}

/* Colored Feature Items */
.feature-blue {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: var(--white) !important;
}

.feature-green {
  background: linear-gradient(135deg, #10b981, #047857) !important;
  color: var(--white) !important;
}

.feature-purple {
  background: linear-gradient(135deg, #8b5cf6, #5b21b6) !important;
  color: var(--white) !important;
}

.feature-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: var(--white) !important;
}

.feature-teal {
  background: linear-gradient(135deg, #14b8a6, #0f766e) !important;
  color: var(--white) !important;
}

.feature-red {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  color: var(--white) !important;
}

.feature-blue h4,
.feature-green h4,
.feature-purple h4,
.feature-orange h4,
.feature-teal h4,
.feature-red h4 {
  color: var(--white) !important;
}

.feature-blue p,
.feature-green p,
.feature-purple p,
.feature-orange p,
.feature-teal p,
.feature-red p {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Services Page */
.service-detail {
  padding: var(--spacing-xxl) 0;
}

.service-detail.alternate {
  background: var(--light-gray);
}

.service-icon-large {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  color: var(--white);
}

.service-packages {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.service-packages h3 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-black);
}

.package-card {
  background: var(--light-gray);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  text-align: center;
  transition: all var(--transition-normal);
}

.package-card.featured {
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  color: var(--white);
  transform: scale(1.05);
}

.package-card h4 {
  margin-bottom: var(--spacing-sm);
}

.package-price {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--primary-black);
}

.package-card.featured .package-price {
  color: var(--white);
}

.package-card ul {
  list-style: none;
  text-align: left;
}

.package-card li {
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.package-card.featured li {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.additional-services {
  background: var(--light-gray);
}

@media (max-width: 767px) {
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .service-icon-large {
    width: 80px;
    height: 80px;
  }
}

/* Gallery Page */
.gallery-filters {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--primary-gold);
  background: transparent;
  color: var(--primary-gold);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
  background: var(--primary-gold);
  color: var(--white);
}

.gallery-grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.gallery-item {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.gallery-item:hover {
  transform: translateY(-4px);
}

.gallery-image {
  position: relative;
  height: 250px;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: var(--white);
  padding: var(--spacing-lg);
  transform: translateY(100%);
  transition: transform var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-overlay h3 {
  color: var(--white);
  margin-bottom: var(--spacing-xs);
}

.gallery-overlay p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  margin: 0;
}

.office-tour {
  background: var(--white);
}

.office-features {
  margin-top: var(--spacing-lg);
}

.office-info {
  background: var(--light-gray);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

.hours-list {
  margin-bottom: var(--spacing-lg);
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.office-address {
  margin-top: var(--spacing-lg);
}

.team-highlights {
  background: var(--light-gray);
}

.highlights-grid {
  display: grid;
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.highlight-item {
  text-align: center;
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.highlight-image {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  color: var(--white);
}

/* FAQ Page */
.faq-section {
  background: var(--white);
}

.faq-category {
  margin-bottom: var(--spacing-xxl);
}

.category-title {
  color: var(--primary-black);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 3px solid var(--primary-gold);
  display: inline-block;
}

.faq-items {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  border: 1px solid var(--light-gray);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
}

.faq-question {
  width: 100%;
  padding: var(--spacing-lg);
  background: var(--white);
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--primary-black);
  transition: background-color var(--transition-fast);
}

.faq-question:hover {
  background: var(--light-gray);
}

.faq-answer {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
  background: var(--light-gray);
  color: var(--dark-gray);
  line-height: 1.6;
}

.still-questions {
  background: var(--light-gray);
}

.questions-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.contact-options {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  margin-top: var(--spacing-xl);
}

.contact-option {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
}

.contact-option h3 {
  color: var(--primary-gold);
  margin-bottom: var(--spacing-sm);
}

.contact-option p {
  font-weight: 600;
  color: var(--primary-black);
  margin-bottom: var(--spacing-xs);
}

.contact-option span {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

/* Contact Page */
.contact-section {
  background: var(--white);
}

.contact-intro {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.contact-intro h2 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-black);
}

.contact-intro p {
  font-size: 1.1rem;
  color: var(--medium-gray);
}

.contact-content {
  display: flex;
  justify-content: center;
  align-items: start;
}

.contact-info h2 {
  margin-bottom: var(--spacing-md);
}

.contact-methods {
  margin: var(--spacing-xl) 0;
}

.contact-method {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--light-gray);
  border-radius: var(--radius-lg);
}

.method-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  border-radius: 50%;
  color: var(--white);
  flex-shrink: 0;
}

.method-info h3 {
  color: var(--primary-black);
  margin-bottom: var(--spacing-xs);
}

.method-info p {
  color: var(--dark-gray);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.method-info span {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

.social-section {
  margin-top: var(--spacing-xl);
}

.social-section h3 {
  margin-bottom: var(--spacing-md);
}

.contact-form-section {
  background: var(--light-gray);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  max-width: 600px;
  width: 100%;
}

/* Contact Methods Section */
.contact-methods-section {
  background: var(--light-gray);
  padding: var(--spacing-xxl) 0;
}

.contact-methods-section .contact-methods {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  max-width: 1000px;
  margin: 0 auto;
}

/* Removed h2 styling since heading was removed */

.contact-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-row {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr 1fr;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--primary-black);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 2px solid var(--light-gray);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-gold);
}

.submit-btn {
  width: 100%;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-gold));
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-gold);
}

.map-section {
  background: var(--light-gray);
}

.map-container {
  margin-top: var(--spacing-lg);
}

.map-container iframe {
  width: 100%;
  height: 450px;
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.emergency-contact {
  background: var(--primary-black);
  color: var(--white);
}

.emergency-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.emergency-content h2 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
}

.emergency-content p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-xl);
}

.emergency-info {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.emergency-item {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
}

.emergency-item h3 {
  color: var(--primary-gold);
  margin-bottom: var(--spacing-sm);
}

.emergency-item p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-md);
}

.emergency-phone {
  color: var(--primary-gold);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color var(--transition-fast);
}

.emergency-phone:hover {
  color: var(--secondary-gold);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contact-content {
    flex-direction: column;
    align-items: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
  }

  .method-icon {
    align-self: center;
  }

  .contact-methods-section .contact-methods {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }
}
