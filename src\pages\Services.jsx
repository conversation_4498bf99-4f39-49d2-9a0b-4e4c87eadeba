import { Link } from 'react-router-dom'
import { Calculator, Home, Shield, FileText, Building, Users, CheckCircle } from 'lucide-react'

const Services = () => {
  return (
    <div className="services">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <div className="page-hero-content">
            <h1>Our Services</h1>
            <p>Comprehensive financial solutions tailored to your needs</p>
          </div>
        </div>
      </section>

      {/* Tax Services */}
      <section className="service-detail">
        <div className="container">
          <div className="content-split">
            <div className="content-left">
              <div className="service-icon-large">
                <Calculator size={60} />
              </div>
              <h2>Tax Services</h2>
              <p>
                Professional tax preparation and planning services for individuals and businesses. 
                Our certified tax professionals ensure you get every deduction you deserve while 
                staying compliant with current tax laws.
              </p>
              <div className="service-features">
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Individual Tax Returns</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Business Tax Preparation</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Tax Planning & Strategy</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>IRS Representation</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Quarterly Estimated Taxes</span>
                </div>
              </div>
            </div>
            <div className="content-right">
              <div className="service-packages">
                <h3>Tax Packages</h3>
                <div className="package-card">
                  <h4>Basic Individual</h4>
                  <div className="package-price">Starting at $99</div>
                  <ul>
                    <li>Form 1040</li>
                    <li>Standard deductions</li>
                    <li>E-filing included</li>
                  </ul>
                </div>
                <div className="package-card featured">
                  <h4>Business Complete</h4>
                  <div className="package-price">Starting at $299</div>
                  <ul>
                    <li>Business returns</li>
                    <li>Quarterly planning</li>
                    <li>Year-round support</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Real Estate Services */}
      <section className="service-detail alternate">
        <div className="container">
          <div className="content-split">
            <div className="content-left">
              <div className="service-packages">
                <h3>Real Estate Solutions</h3>
                <div className="package-card">
                  <h4>Buyer Services</h4>
                  <div className="package-price">Commission-based</div>
                  <ul>
                    <li>Property search</li>
                    <li>Market analysis</li>
                    <li>Negotiation support</li>
                  </ul>
                </div>
                <div className="package-card featured">
                  <h4>Seller Services</h4>
                  <div className="package-price">Competitive rates</div>
                  <ul>
                    <li>Property valuation</li>
                    <li>Marketing strategy</li>
                    <li>Full-service listing</li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="content-right">
              <div className="service-icon-large">
                <Home size={60} />
              </div>
              <h2>Real Estate Services</h2>
              <p>
                Whether you're buying your first home, selling a property, or looking to join 
                our brokerage, we provide expert guidance throughout the entire real estate process. 
                Our experienced agents know the local market inside and out.
              </p>
              <div className="service-features">
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Residential Buying & Selling</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Commercial Real Estate</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Property Management</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Investment Properties</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Agent Recruitment</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Health Insurance Services */}
      <section className="service-detail">
        <div className="container">
          <div className="content-split">
            <div className="content-left">
              <div className="service-icon-large">
                <Shield size={60} />
              </div>
              <h2>Health Insurance</h2>
              <p>
                Navigate the complex world of health insurance with confidence. We help individuals 
                and families find the right coverage that fits their needs and budget, whether through 
                the marketplace, employer plans, or private insurance.
              </p>
              <div className="service-features">
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Individual & Family Plans</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Medicare Enrollment</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Group Health Plans</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Plan Comparison</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Enrollment Assistance</span>
                </div>
              </div>
            </div>
            <div className="content-right">
              <div className="service-packages">
                <h3>Insurance Services</h3>
                <div className="package-card">
                  <h4>Consultation</h4>
                  <div className="package-price">Free</div>
                  <ul>
                    <li>Needs assessment</li>
                    <li>Plan comparison</li>
                    <li>Initial guidance</li>
                  </ul>
                </div>
                <div className="package-card featured">
                  <h4>Full Enrollment</h4>
                  <div className="package-price">No cost to you</div>
                  <ul>
                    <li>Complete enrollment</li>
                    <li>Ongoing support</li>
                    <li>Annual reviews</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="additional-services">
        <div className="container">
          <div className="section-header">
            <h2>Additional Services</h2>
            <p>Comprehensive support for all your financial needs</p>
          </div>
          <div className="services-grid">
            <div className="service-card">
              <FileText size={40} />
              <h3>Financial Planning</h3>
              <p>Comprehensive financial planning to help you achieve your long-term goals.</p>
            </div>
            <div className="service-card">
              <Building size={40} />
              <h3>Business Consulting</h3>
              <p>Strategic business advice to help your company grow and succeed.</p>
            </div>
            <div className="service-card">
              <Users size={40} />
              <h3>Estate Planning</h3>
              <p>Protect your family's future with proper estate planning and documentation.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Contact us today to discuss your needs and learn how we can help you achieve your goals.</p>
            <Link to="/contact" className="btn btn-primary">Schedule Consultation</Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Services
