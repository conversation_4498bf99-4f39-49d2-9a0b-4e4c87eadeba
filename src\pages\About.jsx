import { Users, Target, Award, Heart } from 'lucide-react'
import huyenTonPhoto from '../assets/images/Cindy Ton.jpg'
import brandanShermanPhoto from '../assets/images/Brandon Sherman.jpg'
import kevinPhamPhoto from '../assets/images/<PERSON>.png'
import teamPhoto from '../assets/images/TNGheader1.jpg'

const About = () => {
  return (
    <div className="about">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <div className="page-hero-content">
            <h1>About TonnuGroup</h1>
            <p>Your trusted partner in financial success since 2014</p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="our-story">
        <div className="container">
          <div className="content-split">
            <div className="content-left">
              <h2>Our Story</h2>
              <p>
                Founded in 2014, TonnuGroup began with a simple mission: to provide comprehensive, 
                personalized financial services that help individuals and families achieve their dreams. 
                What started as a small tax preparation service has grown into a full-service financial 
                solutions company.
              </p>
              <p>
                Today, we proudly serve hundreds of clients across multiple states, offering expert 
                guidance in tax preparation, real estate transactions, and health insurance enrollment. 
                Our success is built on trust, expertise, and an unwavering commitment to our clients' 
                financial well-being.
              </p>
              <p>
                We believe that everyone deserves access to professional financial guidance, regardless 
                of their current situation. That's why we work tirelessly to make our services accessible, 
                affordable, and tailored to each client's unique needs.
              </p>
            </div>
            <div className="content-right">
              <div className="team-photo-container">
                <img src={teamPhoto} alt="TonnuGroup Team" className="team-photo" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Values */}
      <section className="mission-values">
        <div className="container">
          <div className="section-header">
            <h2>Our Mission & Values</h2>
            <p>The principles that guide everything we do</p>
          </div>
          <div className="values-grid">
            <div className="value-card">
              <div className="value-icon">
                <Target size={40} />
              </div>
              <h3>Our Mission</h3>
              <p>
                To empower individuals and families with expert financial guidance, 
                helping them navigate complex decisions with confidence and achieve 
                their long-term financial goals.
              </p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <Award size={40} />
              </div>
              <h3>Excellence</h3>
              <p>
                We maintain the highest standards of professional service, staying 
                current with industry changes and continuously improving our expertise 
                to serve you better.
              </p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <Heart size={40} />
              </div>
              <h3>Integrity</h3>
              <p>
                Honesty and transparency are at the core of everything we do. We build 
                lasting relationships based on trust and always act in our clients' 
                best interests.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="team-section">
        <div className="container">
          <div className="section-header">
            <h2>Meet Our Team</h2>
            <p>Experienced professionals dedicated to your success</p>
          </div>
          <div className="team-grid">
            <div className="team-member">
              <div className="member-photo">
                <img src={huyenTonPhoto} alt="Huyen Ton" />
              </div>
              <h3>Huyen Ton</h3>
              <p className="member-title">Founder & CEO</p>
              <p className="member-bio">
                With over 15 years in financial services, Huyen founded TonnuGroup
                to provide personalized financial solutions that make a real difference
                in people's lives.
              </p>
            </div>
            <div className="team-member">
              <div className="member-photo">
                <img src={brandanShermanPhoto} alt="Brandon Sherman" />
              </div>
              <h3>Brandon Sherman</h3>
              <p className="member-title">Commercial Real Estate</p>
              <p className="member-bio">
                A certified commercial real estate specialist with 12 years of experience, Brandon
                specializes in complex commercial transactions and investment properties.
              </p>
            </div>
            <div className="team-member">
              <div className="member-photo">
                <img src={kevinPhamPhoto} alt="Kevin Pham" />
              </div>
              <h3>Kevin Pham</h3>
              <p className="member-title">Senior Tax Advisor</p>
              <p className="member-bio">
                Licensed tax advisor with 10 years of experience helping
                clients navigate complex tax situations and maximize their returns.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="why-choose">
        <div className="container">
          <div className="section-header">
            <h2>Why Choose TonnuGroup?</h2>
          </div>
          <div className="features-grid">
            <div className="feature-item feature-blue">
              <h4>Comprehensive Services</h4>
              <p>All your financial needs under one roof - taxes, real estate, and insurance.</p>
            </div>
            <div className="feature-item feature-green">
              <h4>Personalized Approach</h4>
              <p>We take the time to understand your unique situation and goals.</p>
            </div>
            <div className="feature-item feature-purple">
              <h4>Licensed Professionals</h4>
              <p>Our team holds all necessary licenses and certifications.</p>
            </div>
            <div className="feature-item feature-orange">
              <h4>Proven Track Record</h4>
              <p>Over 10,000 satisfied clients Between TX and Florida</p>
            </div>
            <div className="feature-item feature-teal">
              <h4>Transparent Pricing</h4>
              <p>No hidden fees - you'll know exactly what you're paying upfront.</p>
            </div>
            <div className="feature-item feature-red">
              <h4>Ongoing Support</h4>
              <p>We're here for you even after your transaction is complete.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About
