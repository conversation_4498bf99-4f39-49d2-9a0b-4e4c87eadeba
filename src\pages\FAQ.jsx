import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'

const FAQ = () => {
  const [openItems, setOpenItems] = useState({})

  const toggleItem = (id) => {
    setOpenItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  const faqCategories = [
    {
      category: 'Tax Services',
      items: [
        {
          id: 'tax-1',
          question: 'When should I file my taxes?',
          answer: 'The tax filing deadline is typically April 15th, but we recommend filing as early as possible to avoid delays and get your refund faster. We can help you gather the necessary documents and file your return efficiently.'
        },
        {
          id: 'tax-2',
          question: 'What documents do I need for tax preparation?',
          answer: 'You\'ll need your W-2s, 1099s, receipts for deductible expenses, previous year\'s tax return, and any other relevant financial documents. We\'ll provide you with a comprehensive checklist when you schedule your appointment.'
        },
        {
          id: 'tax-3',
          question: 'How much do your tax services cost?',
          answer: 'Our tax preparation fees start at $99 for basic individual returns. Business returns start at $299. The final cost depends on the complexity of your return. We provide transparent pricing with no hidden fees.'
        },
        {
          id: 'tax-4',
          question: 'Can you help if I owe back taxes to the IRS?',
          answer: 'Yes, we offer IRS representation services and can help you set up payment plans, negotiate settlements, or resolve tax issues. Our experienced team will work with you to find the best solution for your situation.'
        }
      ]
    },
    {
      category: 'Real Estate',
      items: [
        {
          id: 'real-1',
          question: 'How do I know if I\'m ready to buy a home?',
          answer: 'Generally, you should have stable income, good credit, and enough savings for a down payment and closing costs. We can help you assess your readiness and connect you with mortgage professionals to get pre-approved.'
        },
        {
          id: 'real-2',
          question: 'What\'s the difference between a real estate agent and a broker?',
          answer: 'A real estate agent helps buy and sell properties, while a broker has additional training and can supervise agents. Our brokers have extensive experience and can handle complex transactions.'
        },
        {
          id: 'real-3',
          question: 'How long does it typically take to sell a home?',
          answer: 'The average time varies by market conditions, but typically ranges from 30-90 days. We use strategic marketing and pricing to help sell your home as quickly as possible while maximizing your return.'
        },
        {
          id: 'real-4',
          question: 'Do you help with commercial real estate?',
          answer: 'Yes, we handle both residential and commercial real estate transactions. Our team has experience with office buildings, retail spaces, warehouses, and investment properties.'
        }
      ]
    },
    {
      category: 'Health Insurance',
      items: [
        {
          id: 'health-1',
          question: 'When can I enroll in health insurance?',
          answer: 'Open enrollment typically runs from November 1 to December 15, but you may qualify for special enrollment periods due to life events like marriage, job loss, or moving. We can help determine your eligibility.'
        },
        {
          id: 'health-2',
          question: 'What\'s the difference between HMO and PPO plans?',
          answer: 'HMO plans typically have lower costs but require you to choose a primary care physician and get referrals for specialists. PPO plans offer more flexibility but usually cost more. We\'ll help you understand which is best for your needs.'
        },
        {
          id: 'health-3',
          question: 'Do you charge for health insurance enrollment help?',
          answer: 'No, our health insurance enrollment assistance is provided at no cost to you. We\'re compensated by the insurance companies, so our services are free for individuals and families seeking coverage.'
        },
        {
          id: 'health-4',
          question: 'Can you help with Medicare enrollment?',
          answer: 'Absolutely! We specialize in helping seniors navigate Medicare options, including Medicare Advantage plans, Medigap policies, and prescription drug coverage. We make the process simple and stress-free.'
        }
      ]
    },
    {
      category: 'General',
      items: [
        {
          id: 'general-1',
          question: 'How do I schedule an appointment?',
          answer: 'You can schedule an appointment by calling us at (*************, using our online contact form, or visiting our office. We offer flexible scheduling including evenings and weekends when needed.'
        },
        {
          id: 'general-2',
          question: 'Do you offer virtual consultations?',
          answer: 'Yes, we offer virtual consultations via video call for your convenience. This is especially helpful for initial consultations and follow-up meetings. In-person meetings are available for document signing and complex discussions.'
        },
        {
          id: 'general-3',
          question: 'What areas do you serve?',
          answer: 'We primarily serve the local metropolitan area but can work with clients throughout the state for many of our services. Contact us to discuss your specific location and needs.'
        },
        {
          id: 'general-4',
          question: 'How do you protect my personal information?',
          answer: 'We take data security very seriously. All client information is encrypted, stored securely, and only accessed by authorized personnel. We comply with all relevant privacy regulations and industry best practices.'
        }
      ]
    }
  ]

  return (
    <div className="faq">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <div className="page-hero-content">
            <h1>Frequently Asked Questions</h1>
            <p>Find answers to common questions about our services</p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="faq-section">
        <div className="container">
          {faqCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="faq-category">
              <h2 className="category-title">{category.category}</h2>
              <div className="faq-items">
                {category.items.map((item) => (
                  <div key={item.id} className="faq-item">
                    <button
                      className="faq-question"
                      onClick={() => toggleItem(item.id)}
                      aria-expanded={openItems[item.id] || false}
                    >
                      <span>{item.question}</span>
                      {openItems[item.id] ? (
                        <ChevronUp size={20} />
                      ) : (
                        <ChevronDown size={20} />
                      )}
                    </button>
                    {openItems[item.id] && (
                      <div className="faq-answer">
                        <p>{item.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Still Have Questions Section */}
      <section className="still-questions">
        <div className="container">
          <div className="questions-content">
            <h2>Still Have Questions?</h2>
            <p>
              Can't find the answer you're looking for? Our team is here to help. 
              Contact us directly and we'll be happy to assist you with any questions 
              about our services.
            </p>
            <div className="contact-options">
              <div className="contact-option">
                <h3>Call Us</h3>
                <p>(*************</p>
                <span>Monday - Friday, 9 AM - 6 PM</span>
              </div>
              <div className="contact-option">
                <h3>Email Us</h3>
                <p><EMAIL></p>
                <span>We respond within 24 hours</span>
              </div>
              <div className="contact-option">
                <h3>Visit Our Office</h3>
                <p>601 N Plano Rd, Richardson, TX 75081</p>
                <span>Schedule an appointment</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default FAQ
