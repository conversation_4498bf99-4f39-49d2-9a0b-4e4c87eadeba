import { Link } from 'react-router-dom'
import { Calculator, Home as HomeIcon, Shield, Users, CheckCircle, Star } from 'lucide-react'
import heroImage from '../assets/images/photo1.avif'

const Home = () => {
  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-background">
          <img src={heroImage} alt="TonnuGroup Professional Services" className="hero-image" />
          <div className="hero-overlay"></div>
        </div>
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title">
              Your Trusted Partner for <span className="highlight">Financial Success</span>
            </h1>
            <p className="hero-subtitle">
              Expert tax services, real estate solutions, and health insurance guidance 
              to help you achieve your financial goals with confidence.
            </p>
            <div className="hero-buttons">
              <Link to="/contact" className="btn btn-primary">Dallas Office</Link>
              <Link to="/contact" className="btn btn-white-outline">Tampa Office</Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services-preview">
        <div className="container">
          <div className="section-header">
            <h2>Our Core Services</h2>
            <p>Real Estate & Financial Solutions Tailored To Your Needs</p>
          </div>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-icon">
                <Calculator size={40} />
              </div>
              <h3>Tax Services</h3>
              <p>Professional tax preparation and filing for individuals and businesses. Maximize your returns with our expert guidance.</p>
              <Link to="/services" className="service-link">Learn More</Link>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <HomeIcon size={40} />
              </div>
              <h3>Real Estate</h3>
              <p>Complete real estate services including buying, selling, and brokerage. Your property goals are our priority.</p>
              <Link to="/services" className="service-link">Learn More</Link>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <Shield size={40} />
              </div>
              <h3>Health Insurance</h3>
              <p>Find the perfect health insurance plan for you and your family. Navigate options with confidence.</p>
              <Link to="/services" className="service-link">Learn More</Link>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="why-choose-us">
        <div className="container">
          <div className="content-split">
            <div className="content-left">
              <h2>Why Choose TonnuGroup?</h2>
              <p>With years of experience and a commitment to excellence, we provide personalized solutions that deliver real results.</p>
              <div className="features-list">
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Licensed and certified professionals</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Personalized service approach</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Competitive rates and transparent pricing</span>
                </div>
                <div className="feature-item">
                  <CheckCircle size={20} />
                  <span>Comprehensive support throughout the process</span>
                </div>
              </div>
              <Link to="/about" className="btn btn-primary">Learn About Us</Link>
            </div>
            <div className="content-right">
              <div className="stats-grid">
                <div className="stat-item">
                  <div className="stat-number">10,000+</div>
                  <div className="stat-label">Happy Clients</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">13+</div>
                  <div className="stat-label">Years Experience</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">Dallas & Florida</div>
                  <div className="stat-label">Locations</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">24/7</div>
                  <div className="stat-label">Support</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials">
        <div className="container">
          <div className="section-header">
            <h2>What Our Clients Say</h2>
            <p>Real experiences from real people</p>
          </div>
          <div className="testimonials-grid">
            <div className="testimonial-card">
              <div className="stars">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={16} fill="currentColor" />
                ))}
              </div>
              <p>"TonnuGroup made my tax filing so easy and saved me money I didn't know I could save. Highly recommended!"</p>
              <div className="testimonial-author">
                <strong>Sarah Johnson</strong>
                <span>Small Business Owner</span>
              </div>
            </div>
            <div className="testimonial-card">
              <div className="stars">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={16} fill="currentColor" />
                ))}
              </div>
              <p>"They helped us find the perfect home and guided us through every step. Professional and caring service."</p>
              <div className="testimonial-author">
                <strong>Mike & Lisa Chen</strong>
                <span>Homebuyers</span>
              </div>
            </div>
            <div className="testimonial-card">
              <div className="stars">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={16} fill="currentColor" />
                ))}
              </div>
              <p>"Finding health insurance was overwhelming until TonnuGroup simplified everything for our family."</p>
              <div className="testimonial-author">
                <strong>David Rodriguez</strong>
                <span>Family of Four</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Contact us today for a free consultation and discover how we can help you achieve your financial goals.</p>
            <div className="cta-buttons">
              <Link to="/contact" className="btn btn-primary">Contact</Link>
              <Link to="/faq" className="btn btn-white-outline">Find Us</Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
